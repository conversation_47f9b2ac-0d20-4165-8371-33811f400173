package com.ecco.webApi.clients;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.dom.commands.*;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.security.SecurityUtil;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.viewModels.MoveServiceRecipientCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;


@Component
public class MoveServiceRecipientCommandHandler extends ServiceRecipientCommandHandler<
        MoveServiceRecipientCommandViewModel, MoveServiceRecipientCommand, @NonNull DeleteServiceRecipientParams> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final ReferralRepository referralRepository;

    @NonNull
    private final ServiceRecipientRepository serviceRecipientRepository;

    @Autowired
    public MoveServiceRecipientCommandHandler(ObjectMapper objectMapper,
                                              @NonNull ReferralRepository referralRepository,
                                              @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                              @NonNull ServiceRecipientRepository serviceRecipientRepository) {
        super(objectMapper, serviceRecipientCommandRepository, MoveServiceRecipientCommandViewModel.class);
        this.referralRepository = referralRepository;
        this.serviceRecipientRepository = serviceRecipientRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull DeleteServiceRecipientParams params,
                                           @NonNull MoveServiceRecipientCommandViewModel viewModel) {

        // both a move c-id and a move service allocation could be considered the equivalent of deleting the previous file
        SecurityUtil.assertAuthority("ROLE_DELETEREFERRAL");

        BaseServiceRecipientEvidence sr = this.serviceRecipientRepository.findEvidenceCapableById(params.serviceRecipientId).orElseThrow();
        Assert.isTrue(sr instanceof ReferralServiceRecipient); // only do referrals for now

        if (viewModel.parentId != null) {
            moveReferralToClient(sr, viewModel);
        } else {
            moveReferralToServiceAllocation(params, viewModel);
        }
        return null;
    }

    private CommandResult moveReferralToClient(BaseServiceRecipientEvidence sr, MoveServiceRecipientCommandViewModel viewModel) {
        Integer siblingsCount = sr.getTargetEntity().countSiblings();
        referralRepository.moveReferralToClient(sr.getId(), viewModel.parentId);
        if (viewModel.deleteParentIfPossible && (siblingsCount != null && siblingsCount == 1)) {
            entityManager.remove(sr.getTargetEntity().getGrandParent());
        }
        return null;
    }

    private CommandResult moveReferralToServiceAllocation(DeleteServiceRecipientParams params, MoveServiceRecipientCommandViewModel viewModel) {
        referralRepository.moveReferralToSvcAllocation(params.serviceRecipientId, viewModel.serviceAllocationId);
        return null;
    }

    @NonNull
    @Override
    protected MoveServiceRecipientCommand createCommand(Serializable targetId, @NonNull DeleteServiceRecipientParams params,
                                                        @NonNull String requestBody,
                                                        @NonNull MoveServiceRecipientCommandViewModel viewModel,
                                                        long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        return new MoveServiceRecipientCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId);
    }

}
