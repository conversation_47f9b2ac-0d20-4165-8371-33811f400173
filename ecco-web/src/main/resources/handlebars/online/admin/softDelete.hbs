{{#> partials/material-ui-page
        bootstrapRequired="false"
        calendarRequired="false"
        jqplotRequired="false"
        title="buildings"
        importRequireJsModules="environment referral/softDeleteInit"
}}
    <div id="maincontrol" class="referral-router">
        <div style="padding: 8px; background-color: #0d83ca;">
            <a href="{{applicationProperties.applicationRootPath}}nav/r/welcome">
                <img src="{{applicationProperties.resourceRootPath}}themes/ecco/images/logo_white.png" height="48">
            </a>
        </div>
        <div class="">
            <div id="mountpoint" class="container text-center">

                <div class="content">
                    <div class="container-fluid">
                        <form class="form form-horizontal" id="clientFrm">
                            <div class="row">
                                <div class="col-sm-8 col-sm-offset-2">
                                    <h4>enter some client details to find referrals</h4>

                                    <div id="notFoundMsg" class="alert alert-warning hidden">no matches were found</div>
                                    <div id="badRequestMsg" class="alert alert-danger hidden">foolish request</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="srIdInp" class="control-label col-sm-2 col-sm-offset-2">sr-id</label>
                                <div class="col-sm-6">
                                    <input id="srIdInp" name="srId" type="text" class="form-control" autocomplete="off"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="firstNameInp" class="control-label col-sm-2 col-sm-offset-2">first name</label>
                                <div class="col-sm-6">
                                    <input id="firstNameInp" name="firstName" type="text" class="form-control" autocomplete="off"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="lastNameInp" class="control-label col-sm-2 col-sm-offset-2">last name</label>
                                <div class="col-sm-6">
                                    <input id="lastNameInp" name="lastName" type="text" class="form-control" autocomplete="off" autofocus/>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-6 col-sm-offset-4">
                                    <button type="submit" class="btn btn-primary">
                                        <span class="glyphicon glyphicon-search"></span> find referrals
                                    </button>
                                </div>
                            </div>
                        </form>
                        <br>
                        <table id="referralTbl" class="table table-condensed hidden">
                            <thead>
                            <tr>
                                <th>cid</th>
                                <th>rid</th>
                                <th>name</th>
                                <th>service</th>
                                <th>status</th>
                                <th>actions status</th>
                                <th>actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="referral-record hidden">
                                <td class="col-md-1 referral-cid"></td>
                                <td class="col-md-1 referral-rid"></td>
                                <td class="col-md-1 referral-name"></td>
                                <td class="col-md-1 referral-service"></td>
                                <td class="col-md-1 referral-status"></td>
                                <td class="col-md-4 referral-actionStatusWrapper">
                                    <span class="referral-actionStatus"></span>
                                    <span class="referral-requestDeleteUuid" style="display: none;"></span>
                                    <span class="referral-requestDeleteParent" style="display: none;"></span>
                                </td>
                                <td>
                                    <button type="submit" class="hidden requestDeleteReferralBtn btn btn-xs btn-warning">request delete</button>
                                    <button type="submit" class="hidden moveCidReferralBtn btn btn-xs btn-warning">move c-id</button>
                                    <button type="submit" class="hidden moveSvcReferralBtn btn btn-xs btn-warning">move service</button>
                                    <button type="submit" class="hidden revokeRequestDeleteReferralBtn btn btn-xs btn-warning">cancel request</button>
                                    <button type="submit" class="hidden deleteReferralBtn btn btn-xs btn-danger">delete</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>

    </div>
    <div id="snackbar"></div>
{{/partials/material-ui-page}}