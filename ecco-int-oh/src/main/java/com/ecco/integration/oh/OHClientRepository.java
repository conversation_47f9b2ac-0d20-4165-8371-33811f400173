package com.ecco.integration.oh;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OHClientRepository extends JpaRepository<OHClient, Integer> {

    /**
     * Limited in the SP by 100 results?
     * @param search searches on person_ref, and full name, and first initial + surname
     */
    @Procedure("ECCO_Person_Search")
    List<OHClient> getClients(@Param("Search") String search);

}
