package com.ecco.integration.oh;

import lombok.Data;

import javax.persistence.Column;

@Data
public class OHClient {

    @Column(name = "ID")
    private long id;

    @Column(name = "PersonReference")
    private String personRef;

    @Column(name = "FirstName")
    private String firstName;

    @Column(name = "Initials")
    private String initials;

    @Column(name = "LastName")
    private String lastName;

    @Column(name = "PersonName")
    private String fullName;

    @Column(name = "Gender")
    private String gender;

    @Column(name = "NI")
    private String ni;

    @Column(name = "DOB")
    private String dateOfBirth;

    @Column(name = "PropertyReference")
    private String propertyRef;

    @Column(name = "AddLine1")
    private String addressLine1;

    @Column(name = "AddLine2")
    private String addressLine2;

    @Column(name = "AddLine3")
    private String addressLine3;

    @Column(name = "AddLine4")
    private String addressLine4;

    @Column(name = "AddLine5")
    private String addressLine5;

    @Column(name = "PostCode")
    private String addressPostCode;

    @Column(name = "PropertyAddress")
    private String addressFull;

}