package com.ecco.integration.oh;

import static org.springframework.http.HttpStatus.OK;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import java.util.*;

import com.ecco.dto.*;
import com.ecco.integration.core.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.web.bind.annotation.*;


/**
 * Provides an implementation of the default REST API which delegates to OH to return its results.
 */
@RestController
@RequestMapping("/oh")
@Slf4j
public class OHClientQueryAdapter {

    private final Properties referenceDataMapping;
    private final OHClientRepository clientRepository;

    @Autowired
    public OHClientQueryAdapter(
            @Qualifier("referenceDataMapping") Properties referenceDataMapping,
            OHClientRepository clientRepository) {
        this.referenceDataMapping = referenceDataMapping;
        this.clientRepository = clientRepository;
    }

    @GetJson(value = "/check-log/")
    public String checkLogs() {
        log.trace("A TRACE Message");
        log.debug("A DEBUG Message");
        log.info("An INFO Message");
        log.warn("A WARN Message");
        log.error("An ERROR Message");

        return "Check out the Logs to see the log output...";
    }

    /**
     * ClientDefinition here comes from ClientController -> ClientDefinitionFromViewModel which gets the business keys
     */
    @PostJson(value = "/clients/query", produces = APPLICATION_JSON_VALUE)
    @ResponseStatus(OK)
    public Collection<ClientDefinition> queryClientsByExample(@RequestBody final ClientDefinition exemplar) {

        var clients = clientRepository.getClients(exemplar.getLastName().toLowerCase() + "%");
        return clients.stream().map(c ->
            ClientDefinition.BuilderFactory.create()
                .externalClientRef(c.getPersonRef())
                .build()
        ).toList();
    }

    /** Attempt to resolve the returned value using the supplied prefix and returned value as the suffix */
    private String getMappedReferenceData( String prefix, String key) {
        return Utils.getMappedReferenceData(referenceDataMapping, prefix, key);
    }

}
