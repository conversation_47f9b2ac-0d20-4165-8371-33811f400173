import $ = require("jquery");
import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextAreaInput = require("../controls/TextAreaInput");
import {WebApiError} from "@eccosolutions/ecco-common";
import {
    DeleteRequestServiceRecipientCommand,
    DeleteServiceRecipientCommand,
    MoveServiceRecipientCommand
} from "ecco-commands";
import {apiClient} from "ecco-components";
import {
    ClientAjaxRepository,
    ReferralAjaxRepository,
    ReferralDto as Referral, SessionData,
    SessionDataAjaxRepository
} from "ecco-dto";
import {ValidationCheck, ValidationChecksBuilder, ValidationErrors} from "../common/validation";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import CheckboxInput from "../controls/CheckboxInput";
import TextInput from "../controls/TextInput";

const sessionDataRepository = new SessionDataAjaxRepository(apiClient);

/**
 * Manages the soft delete page.
 */
class SoftDeleteControl {
    constructor(private referralRepository: ReferralAjaxRepository, private clientRepository: ClientAjaxRepository, private $clientFrm: $.JQuery, private $referralTbl: $.JQuery) {
        $clientFrm.on('submit', (e) => { this.clientFormSubmitted(e) });
    }

    private clientFormSubmitted(e: $.JQueryEventObject) {
        e.preventDefault();
        this.resetDynamicState();
        this.enableSearch(false);
        var $srIdInp = this.$clientFrm.find("input[name='srId']");
        var $firstNameInp = this.$clientFrm.find("input[name='firstName']");
        var $lastNameImp = this.$clientFrm.find("input[name='lastName']");

        const sessionDataQ = sessionDataRepository.getSessionData();
        // NB since the hibernate filter was removed, this find 'unfiltered' by name is equivalent to just find by name
        sessionDataQ.then(sd => {
          const rQ = $srIdInp.val().trim() !== ""
                  ? this.referralRepository.findOneReferralByServiceRecipientId(parseInt($srIdInp.val().trim()))
                      .then(r => {return [r]})
                  : this.referralRepository.findUnfilteredReferralsByClientName($firstNameInp.val().trim(), $lastNameImp.val().trim());
          return rQ.then((referrals) => {
              if (!referrals.length) {
                  this.$clientFrm.find(".alert-warning").text('no matching referrals found').removeClass("hidden");
              } else {
                  this.referralsLoaded(sd, referrals);
              }
              this.enableSearch();
          })
          .catch((error: WebApiError) => {
              $lastNameImp.focus().closest(".form-group").addClass("has-error");
              this.$clientFrm.find(".alert-danger").text(error.reason.message).removeClass("hidden");
              this.enableSearch();
          })
      }
        );
    }

    private resetDynamicState() {
        this.$clientFrm.find(".alert").addClass("hidden");
        this.$referralTbl.addClass("hidden");
        this.$clientFrm.find(".form-group").removeClass("has-error");
    }

    private enableSearch(enabled?:boolean) {
        enabled = enabled || true;
        this.$clientFrm.find("button").prop('disabled', !enabled);
    }

    private referralsLoaded(sessionData: SessionData, referrals: Referral[]) {
        this.$referralTbl.find(".referral-record").not(".hidden").remove();
        this.fillReferrals(sessionData, referrals);
        this.$referralTbl.removeClass("hidden");
    }

    private fillReferrals(sessionData, referrals: Referral[]) {
        var $template = this.$referralTbl.find(".referral-record").first();
        var $parent = $template.parent();

        $.each(referrals, (idx, referral: Referral) => {
            var $referral = $template.clone().removeClass('hidden');
            $referral.find('.referral-cid').text(referral.clientCode || referral.clientId.toString());
            $referral.find('.referral-rid').text(referral.referralCode || referral.referralId.toString());
            $referral.find('.referral-name').text(referral.clientDisplayName);
            $referral.find('.referral-service').text(sessionData.getServiceCategorisation(referral.serviceAllocationId).serviceName);
            $referral.find('.requestDeleteReferralBtn').on('click', (e) => { this.requestDeleteReferralSelected(referral, e) });
            $referral.find('.moveCidReferralBtn').on('click', (e) => { this.moveCidReferralSelected(referral, e) });
            $referral.find('.moveSvcReferralBtn').on('click', (e) => { this.moveSvcReferralSelected(referral, e) });
            $referral.find('.revokeRequestDeleteReferralBtn').on('click', (e) => { this.revokeRequestDeleteReferralSelected(referral, e) });
            $referral.find('.deleteReferralBtn').on('click', (e) => { this.deleteReferralSelected(referral, e) });
            this.updateReferralRow(false, false, $referral, referral);

            $referral.appendTo($parent);
        });
    }

    private updateReferralRow(isDeleted: boolean, isMoved: boolean, $referral: $.JQuery, referral: Referral) {
        if (isDeleted) {
            $referral.find('.referral-actionStatus').html("<b>deleted</b>");
            $referral.find('.btn').hide();
        } else if (isMoved) {
            $referral.find('.referral-actionStatus').html("<b>moved</b>");
            $referral.find('.btn').hide();
        } else {
            const sessionDataQ = sessionDataRepository.getSessionData();
            const reqQ = this.referralRepository.findLatestServiceRecipientDeleteRequest(referral.serviceRecipientId);
            Promise.all([sessionDataQ, reqQ]).then(([sessionData, request]) => {
                    const messages = sessionData.getMessages()
                    let requestedDelete = !!referral.requestedDelete; // default to LEGACY when no command might exist yet
                    let deleteStatus = requestedDelete ? messages['status.hidden'] : null; // default to LEGACY
                    if (request) {
                        requestedDelete = !request.revoke;
                        deleteStatus = request.revoke ? "revoked" : messages['status.hidden'];
                        deleteStatus = "<b>" + deleteStatus + "</b>"
                            .concat(" [")
                            .concat(" at " + request.timestamp)
                            .concat(" by " + request.userName + "] ")
                            .concat(request.reason);
                    }
                    $referral.find('.referral-status').text(messages[referral.statusMessageKey]);
                    $referral.find('.referral-actionStatus').html(deleteStatus);
                    $referral.find('.requestDeleteReferralBtn').toggleClass("hidden", requestedDelete);
                    $referral.find('.moveCidReferralBtn').toggleClass("hidden", sessionData.hasRoleReferralDelete() ? requestedDelete : true);
                    $referral.find('.moveSvcReferralBtn').toggleClass("hidden", sessionData.hasRoleReferralDelete() ? requestedDelete : true);
                    $referral.find('.referral-requestDeleteUuid').text(request ? request.uuid : null);
                    $referral.find('.referral-requestDeleteParent').text(request ? request.deleteParentIfPossible : null);
                    $referral.find('.revokeRequestDeleteReferralBtn').toggleClass("hidden", !requestedDelete);
                    $referral.find('.deleteReferralBtn').toggleClass("hidden", sessionData.hasRoleReferralDelete() ? !requestedDelete : true);
                });
        }
    }

    private requestDeleteReferralSelected(referral: Referral, e: $.JQueryMouseEventObject) {
        var $currentTarget = $(e.currentTarget);
        var onSubmitted = () => {
            this.updateReferralRow(false, false, $currentTarget.closest('.referral-record'), referral);
        };
        var form = new ActionForm(this.referralRepository, this.clientRepository, actionType.REQUEST, referral.serviceRecipientId, onSubmitted);
        form.load();
        showFormInModalDom(form);
    }

    private revokeRequestDeleteReferralSelected(referral: Referral, e: $.JQueryMouseEventObject) {
        var $currentTarget = $(e.currentTarget);
        var onSubmitted = () => {
            this.updateReferralRow(false, false, $currentTarget.closest('.referral-record'), referral);
        };
        var form = new ActionForm(this.referralRepository, this.clientRepository, actionType.REVOKE, referral.serviceRecipientId, onSubmitted);
        form.load();
        showFormInModalDom(form);
    }

    private deleteReferralSelected(referral: Referral, e: $.JQueryMouseEventObject) {
        var $currentTarget = $(e.currentTarget);
        let requestDeleteUuid = $currentTarget.closest('.referral-record').find('.referral-requestDeleteUuid').text();
        let requestDeleteParent = !!$currentTarget.closest('.referral-record').find('.referral-requestDeleteParent').text();
        let viewModel = JSON.stringify(referral);
        var onSubmitted = () => {
            this.updateReferralRow(true, false, $currentTarget.closest('.referral-record'), referral);
        };
        var form = new ActionForm(this.referralRepository, this.clientRepository, actionType.DELETE, referral.serviceRecipientId, onSubmitted, requestDeleteUuid, requestDeleteParent, viewModel);
        form.load();
        showFormInModalDom(form);
    }

    private moveCidReferralSelected(referral: Referral, e: $.JQueryMouseEventObject) {
        var $currentTarget = $(e.currentTarget);
        var onSubmitted = () => {
            this.updateReferralRow(false, true, $currentTarget.closest('.referral-record'), referral);
        };
        var form = new ActionForm(this.referralRepository, this.clientRepository, actionType.MOVE_CID, referral.serviceRecipientId, onSubmitted);
        form.load();
        showFormInModalDom(form);
    }

    private moveSvcReferralSelected(referral: Referral, e: $.JQueryMouseEventObject) {
        var $currentTarget = $(e.currentTarget);
        var onSubmitted = () => {
            this.updateReferralRow(false, true, $currentTarget.closest('.referral-record'), referral);
        };
        var form = new ActionForm(this.referralRepository, this.clientRepository, actionType.MOVE_SVC, referral.serviceRecipientId, onSubmitted);
        form.load();
        showFormInModalDom(form);
    }

    private enableActions($buttons: $.JQuery, enabled?:boolean) {
        enabled = enabled || true;
        $buttons.prop('disabled', !enabled);
    }

}

enum actionType {
    REQUEST = 0, REVOKE = 1, DELETE = 2, MOVE_CID = 3, MOVE_SVC = 4
}

class ActionForm extends BaseAsyncCommandForm<void> {

    public static showInModal(referralRepository: ReferralAjaxRepository, clientRepository: ClientAjaxRepository, actionType: actionType, serviceRecipientId: number, onSubmitted: () => void) {
        const form = new ActionForm(referralRepository, clientRepository, actionType, serviceRecipientId, onSubmitted);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form();
    private reason = new TextAreaInput("reason");
    private parentIdCode = new TextInput("parentId");
    private deleteParentIfPossible = new CheckboxInput("delete parent if possible");
    private svcAllocId = new TextInput("svcAllocId");

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private referralRepository: ReferralAjaxRepository, private clientRepository: ClientAjaxRepository,
                private actionType: actionType, private serviceRecipientId: number,
                private onSubmitted: () => void,
                private requestDeleteUuid?: string, private requestDeleteParent?: boolean,
                private viewModel?: string) {
        super("reason for request", undefined, "modal-sm");
        this.setOnFinished(this.onSubmitted);
        this.form
            .append( new InputGroup("reason", this.reason) );
    }

    protected fetchViewData(): Promise<void> {
        return Promise.resolve(null);
    }

    protected render() {
        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        if ((this.actionType == actionType.DELETE
                || this.actionType == actionType.REQUEST
                || this.actionType == actionType.MOVE_CID
            )) {
            this.renderDeleteParent();
        }
        if (this.actionType == actionType.MOVE_CID) {
            this.renderMoveCid();
        }
        if (this.actionType == actionType.MOVE_SVC) {
            this.renderMoveSvc();
        }
        this.append(this.form);
    }

    private renderMoveCid() {
        const cIdCodeGrp = new InputGroup("c-id", this.parentIdCode)
            .withValidationChecks(new ValidationChecksBuilder()
            .addCheck(ValidationCheck.Required), new ValidationErrors(""))
            .enableValidation();
        this.form
            .append(cIdCodeGrp);
    }

    private renderDeleteParent() {
        this.deleteParentIfPossible.setChecked(this.requestDeleteParent);
        this.form
            .append(this.deleteParentIfPossible);
    }

    private renderMoveSvc() {
        const svcAllocGrp = new InputGroup("sa-id", this.svcAllocId)
            .withValidationChecks(new ValidationChecksBuilder()
            .addCheck(ValidationCheck.Required), new ValidationErrors(""))
            .enableValidation();
        this.form
            .append(svcAllocGrp);
    }

    protected override submitForm(): Promise<void> {
        let cmd;
        switch (this.actionType) {
            case actionType.REQUEST:
                cmd = new DeleteRequestServiceRecipientCommand(this.serviceRecipientId, this.reason.val(), false, this.deleteParentIfPossible.isChecked());
                break;
            case actionType.REVOKE:
                cmd = new DeleteRequestServiceRecipientCommand(this.serviceRecipientId, this.reason.val(), true, this.deleteParentIfPossible.isChecked());
                break;
            case actionType.DELETE:
                cmd = new DeleteServiceRecipientCommand(this.serviceRecipientId, this.requestDeleteUuid, this.reason.val(), this.deleteParentIfPossible.isChecked(), this.viewModel);
                break;
            case actionType.MOVE_CID:
                return this.clientRepository.findOneClientByCode(this.parentIdCode.val()).then(cs => {
                    if (cs.length != 1) {
                        throw new Error("doesn't match just one, matches "+cs.length);
                    }
                    cmd = new MoveServiceRecipientCommand(this.serviceRecipientId, this.reason.val(), cs[0].clientId, this.deleteParentIfPossible.isChecked());
                    this.commandQueue.addCommand(cmd);
                    return super.submitForm();
                });
            case actionType.MOVE_SVC:
                cmd = new MoveServiceRecipientCommand(this.serviceRecipientId, this.reason.val(), undefined, undefined, parseInt(this.svcAllocId.val()));
                this.commandQueue.addCommand(cmd);
                return super.submitForm();
        }
        this.commandQueue.addCommand(cmd);
        return super.submitForm();
    }

}

export = SoftDeleteControl
