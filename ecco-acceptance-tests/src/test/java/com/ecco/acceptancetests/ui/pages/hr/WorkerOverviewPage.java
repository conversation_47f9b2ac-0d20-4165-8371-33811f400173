package com.ecco.acceptancetests.ui.pages.hr;

import com.ecco.acceptancetests.ui.pages.BasePageObject;

import org.openqa.selenium.WebDriver;

/**
 * @since 29/07/2013
 */
public class WorkerOverviewPage extends BasePageObject {
    private static final String URL = "/dynamic/secure/hr/worker/overview";
    private static final String LINK_LINKED_USER = "linked user";

    public WorkerOverviewPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public LinkUserPage linkedUser() {
        clickLink(LINK_LINKED_USER);
        return new LinkUserPage(getWebDriver());
    }
}
