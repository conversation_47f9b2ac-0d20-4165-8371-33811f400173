package com.ecco.acceptancetests.ui.pages.offline;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.BasePageObject;


public class ManageOfflinePage extends BasePageObject {

    private static final String URL = "/dynamic/online/offline/manage"; // FIXME: This is the old one


    public ManageOfflinePage(WebDriver webDriver) {
        super(URL, webDriver);
    }


    public void clickFinish() {
        // could do with an id, but it's currently only btn-success on page
        clickElementByCssSelector("button.btn-success", 30); // 30 secs as we're waiting for app-cache sync
    }

    public void clickSyncButton() {
        clickElementByCssSelector("#sync-status button");
    }

    public ReferralsListPage gotoReferrals() {
        clickElementByCssSelector("#offline-referrals a.btn");
        return new ReferralsListPage(getWebDriver());
    }
}
