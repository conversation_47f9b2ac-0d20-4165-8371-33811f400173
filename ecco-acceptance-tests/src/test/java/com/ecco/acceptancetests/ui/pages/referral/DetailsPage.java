package com.ecco.acceptancetests.ui.pages.referral;

import java.util.Date;

import com.ecco.data.client.ReferralOptions;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class DetailsPage extends EccoBasePage {

    private static final String FIELD_CRN = "customData['personal.crn']";
    private static final String RADIO_SCHEDULE_1 = "customData['supportRisk.schedule1']";
    private static final String FIELD_LAST_2_ADDRESSES = "customData['personal.previousAddresses']";
    private static final String FIELD_NOSTEL_ADDRESSES = "customData['personal.previousHostelAddresses']";
    private static final String FIELD_REASONS = "customData['personal.reasonsForLeaving']";
    private static final String RADIO_CRIMINAL_DAMAGE = "customData['personal.criminalDamange']";
    private static final String RADIO_RENT_ARREARS = "customData['personal.rentArrears']";
    private static final String RADIO_BREACH_OF_TENANCY = "customData['personal.otherBreachOfTenancy']";
    private static final String FIELD_BREACH_OF_TENANCY = "customData['personal.otherBreachOfTenancyText']";
    private static final String FIELD_LOCAL_ADDRESS = "customData['personal.localConnectionAddress']";
    private static final String FIELD_TIME_IN_AREA = "customData['personal.timeInLocalArea']";
    private static final String FIELD_NEXT_OF_KIN = "customData['personal.nextOfKin']";
    private static final String FIELD_NEXT_OF_KIN_CONTACT = "customData['personal.nextOfKinContactDetails']";
    private static final String FIELD_PERIODS_OUT_OF_COUNTRY = "customData['personal.periodsOutOfTheCountry']";
    private static final String FIELD_MARITAL_STATUS = "customData['personal.maritalStatus']";
    private static final String FIELD_NUMBER_DEPENDANTS    = "customData['personal.numberOfDependentChildren']";
    private static final String FIELD_EX_SERVICEMEN = "customData['personal.exServiceMen']";
    private static final String FIELD_PERSONAL_SUMMARY = "customData['personal.personalSummary']";

    private static final String URL = "/dynamic/secure/referralAspectFlow";

    ReferralViewPage referralPage;
    ReferralOptions options;

    public DetailsPage(ReferralOptions options, ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
        this.options = options;
    }

    /**
     * Default data - none needed, so simply hit the 'next' button
     */
    @Override
    public EccoBasePage defaultAction() {
        setDateByElementId("date-picker-inline-received date", new Date());

        clickActionButton(EccoBasePage.BUTTON_SAVE);
        return referralPage;
    }

}
