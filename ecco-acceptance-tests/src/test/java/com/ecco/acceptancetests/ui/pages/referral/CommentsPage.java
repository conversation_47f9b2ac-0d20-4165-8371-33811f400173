package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class CommentsPage extends EccoBasePage {

    protected static final String URL = "/dynamic/secure/referralFlow";

    private static final String BUTTON_ADDCOMMENT = "_eventId_addComment";

    ReferralViewPage referralPage;
    public CommentsPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    public void comment(String text) {
        getWebDriver().findElement(By.tagName("textarea")).sendKeys(text);
    }

    public void save() {
        clickButton(BUTTON_ADDCOMMENT);
    }

    @Override
    public EccoBasePage defaultAction() {
        comment("some default comment");
        return referralPage;
    }

}
