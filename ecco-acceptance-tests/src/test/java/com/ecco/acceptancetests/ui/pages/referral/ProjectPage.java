package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.BasePageObject;

public class ProjectPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/referralFlow";


    public ProjectPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public FindReferralsPage gotoFirstProject() {
        clickLink("project 5");
        return new FindReferralsPage(getWebDriver());
    }

}
