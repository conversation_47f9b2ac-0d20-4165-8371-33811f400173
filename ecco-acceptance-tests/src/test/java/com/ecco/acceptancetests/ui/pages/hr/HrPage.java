package com.ecco.acceptancetests.ui.pages.hr;

import com.ecco.acceptancetests.ui.pages.BasePageObject;

import org.openqa.selenium.WebDriver;

public class HrPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/entities/workers/get";

    private static final String LINK_ADD_WORKER = "add worker";

    public HrPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public WorkerEditPage gotoAddWorkerPage() {
        verifyIsCurrentPage();
        clickLink(LINK_ADD_WORKER);
        return new WorkerEditPage(getWebDriver());
    }

    public WorkerOverviewPage gotoWorkerDetails(String workerName) {
        verifyIsCurrentPage();
        clickLink(workerName);
        return new WorkerOverviewPage(getWebDriver());
    }
}
