package com.ecco.acceptancetests.ui.pages.offline;

import java.util.List;

import org.openqa.selenium.WebDriver;
import com.ecco.acceptancetests.ui.pages.BasePageObject;

public class ReferralsListPage extends BasePageObject {

	private static final String URL = null;

	public ReferralsListPage(WebDriver webDriver) {
		super(URL, webDriver);
	}

	public List<String> getClientNames() {
		List<String> elements = getElementsByCssSelector(".referral-client");
		return elements;

	}





}
