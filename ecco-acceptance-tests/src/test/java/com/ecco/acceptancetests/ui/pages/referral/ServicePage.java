package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.BasePageObject;

public class ServicePage extends BasePageObject {

    private static final String URL = "/dynamic/secure/referralFlow";

    public ServicePage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public void menu() {
        getPageHeader().gotoMenu();
        waitForPageLoaded();
        verifyIsCurrentPage();
    }

    public ProjectPage followServicePageForProject(String linkText) {
        clickLink(linkText);
        return new ProjectPage(getWebDriver());
    }

    public FindReferralsPage followServicePage(String linkText) {
        clickLink(linkText);
        return new FindReferralsPage(getWebDriver());
    }

}
