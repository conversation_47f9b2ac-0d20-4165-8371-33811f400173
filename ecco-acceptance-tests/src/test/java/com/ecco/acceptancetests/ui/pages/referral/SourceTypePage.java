package com.ecco.acceptancetests.ui.pages.referral;

import static org.junit.Assert.assertTrue;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class SourceTypePage extends EccoBasePage {

    private static final String BUTTON_AGENCY_REFERRAL_ID = "_eventId_agency";
    private static final String BUTTON_SELF_REFERRAL_ID = "_eventId_self";

    private static final String VERIFY_TEXT1 = "professional";

    ReferralViewPage referralPage;
    public SourceTypePage(String url, ReferralViewPage referralPage, WebDriver webDriver) {
        super(url, webDriver);
        this.referralPage = referralPage;
    }

    @Override
    public void verifyIsCurrentPage() {
        super.verifyIsCurrentPage();
        WebElement bodyText = getWebDriver().findElement(By.tagName("body"));
        assertTrue("Incorrect page found - expected to see text " + VERIFY_TEXT1, bodyText.getText().contains(VERIFY_TEXT1));
    }

    /** Referral is from an agency/professional */
    public void fromAgency() {
        clickButton(BUTTON_AGENCY_REFERRAL_ID);
        // TODO: return new AgenciesPage(webDriver);
    }

    public ReferralViewPage selfReferral() {
        clickButton(BUTTON_SELF_REFERRAL_ID);
        return referralPage;
    }

    @Override
    public EccoBasePage defaultAction() {
        return selfReferral();
    }

    /*
     * TODO: agency referral
     * =>
     * Agency Details page: select with ID&name "agency", button ID _eventId_addAgency, button next _eventId_next
     * Add new agency => contact details screen (companyName, mobileNumber, email, etc) + ok button _eventId_next
     * OK => pesonal info, then carry on referral flow as before.
     */

    /*
    public ReferralsFlow nextPage(String serviceName) {
        if ("service 2".equals(serviceName)) {
            return new AccommodationNeededAreaPage(getWebDriver());
        } else {
            return new ReferralDetailsPage(getWebDriver());
        }
    }
    */
}
