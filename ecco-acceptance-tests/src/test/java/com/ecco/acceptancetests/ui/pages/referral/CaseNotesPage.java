package com.ecco.acceptancetests.ui.pages.referral;

import com.ecco.acceptancetests.ui.pages.EccoBasePage;
import com.ecco.acceptancetests.ui.pages.supportplan.EvidenceBasePage;

import org.openqa.selenium.WebDriver;

import java.util.Date;

public class CaseNotesPage extends EvidenceBasePage {

    private static final String URL = "/dynamic/secure/generic/supportStaffNotes/edit.html";

    ReferralViewPage referralPage;

    public CaseNotesPage(ReferralViewPage referralPage, WebDriver webDriver) {
        super(URL, webDriver);
        this.referralPage = referralPage;
    }

    /**
     * Default data - pick the first project.
     */
    public EccoBasePage defaultAction() {
        notes(new Date());
        return referralPage;
    }

    private void notes(final Date notesDate) {
        setWorkDate(notesDate);
        setComment("case notes comment");
        clickButtonByText("save");
    }

}
