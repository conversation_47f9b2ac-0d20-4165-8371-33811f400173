import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eOptional, StringChangeOptional} from "../command-dto";
import {BaseServiceRecipientCommandDto} from "../evidence-dto";
import {ServicesDto} from "../service-config-dto";

export interface DeleteRequestServiceRecipientCommandDto extends BaseServiceRecipientCommandDto {
    /** The service recipient ID of the referral */
    serviceRecipientId: number;
    /** Whether we are trying to revoke a previous delete request */
    revoke: boolean;
    /** The reason for this request */
    reason: string;
    /** option to delete the parent, if possible */
    deleteParentIfPossible: boolean;
}

export interface DeleteServiceRecipientCommandDto extends BaseServiceRecipientCommandDto {
    /** The service recipient ID of the referral */
    serviceRecipientId: number;
    /** The deletion request to act upon */
    requestDeletionUuid: string;
    /** arbitrary data we can inspect details on deletions not created with commands */
    jsonViewModel: string;
    /** The reason for this deletion */
    reason: string;
    /** option to delete the parent, if possible */
    deleteParentIfPossible: boolean;
}

export interface MoveServiceRecipientCommandDto extends BaseServiceRecipientCommandDto {
    /** The service recipient ID of the referral */
    serviceRecipientId: number;
    /** The reason for this action */
    reason: string;
    /** The parent file to move to */
    parentId?: number;
    /** option to delete the parent, if possible */
    deleteParentIfPossible?: boolean;
    /** move the file instead */
    serviceAllocationId?: number;
}

export interface ServiceRecipientAssociatedContactCommandDto extends CommandDto {
    serviceRecipientId: number;
    operation: string;
    contactId: number;
    addedAssociatedTypeIds: StringChangeOptional,
    removedAssociatedTypeIds: StringChangeOptional,
    archivedChange: StringChangeOptional;

    associatedServiceRecipientId: number | undefined;
    associatedRelationship: NumberChangeOptional;
}

export interface ServiceRecipientTaskBaseCommandDto extends BaseServiceRecipientCommandDto {
    serviceRecipientId: number;
    taskName: string;
    userComment?: string | undefined;
}

/** See com.ecco.webApi.taskFlow.DaysAttendingUpdateCommandViewModel */
export interface DaysAttendingUpdateCommandDto extends ServiceRecipientTaskBaseCommandDto {
    daysAttendingChange: NumberChangeOptional;
}

/** see com.ecco.webApi.tasks.ReferralJoinCommandViewModel */
export interface ReferralTaskJoinCommandDto extends ServiceRecipientTaskBaseCommandDto {
    parentServiceRecipientId: NumberChangeOptional;
}

/** see com.ecco.webApi.taskFlow.ReferralTaskAllocateServiceCommandViewModel */
export interface ReferralTaskAllocateServiceCommandDto extends ServiceRecipientTaskBaseCommandDto {
    allocations: ServicesDto;
    allocationIds: number[];
}

/**
 * Currently used for editing child referrals from the 'services' tab
 * see com.ecco.webApi.taskFlow.ReferralTaskEditDetailsCommandViewModel
 */
export interface ReferralTaskEditDetailsCommandDto extends ServiceRecipientTaskBaseCommandDto {
    choicesMapChanges?: {[key: string]: NumberChangeOptional} | undefined;

    daysAttendingChange: NumberChangeOptional;

    exitedDateChange: StringChangeOptional;

    isPrimaryChildReferralChange: BooleanChange;

    projectChange: NumberChangeOptional;

    receivingServiceDateChange: StringChangeOptional;

    textMapChanges?: {[key: string]: StringChangeOptional} | undefined;
}

/** see com.ecco.webApi.taskFlow.ReferralTaskEditSourceCommandViewModel */
export interface ReferralTaskEditSourceCommandDto extends ServiceRecipientTaskBaseCommandDto {

    agencyChange: NumberChangeOptional;
    referrerChange: NumberChangeOptional;
    selfReferralChange: BooleanChange;
}

/** see com.ecco.webApi.taskFlow.ReferralTaskEditDestinationCommandViewModel */
export interface ReferralTaskEditDestinationCommandDto extends ServiceRecipientTaskBaseCommandDto {
    projectChange: NumberChangeOptional;
}

export interface ReferralTaskEditEmergencyDetailsCommandDto extends ServiceRecipientTaskBaseCommandDto {
    descriptionDetails: StringChangeOptional;
    communicationNeeds: StringChangeOptional;
    emergencyKeyword: StringChangeOptional;
    emergencyDetails: StringChangeOptional;
    medicationDetails: StringChangeOptional;
    doctorDetails: StringChangeOptional;
    dentistDetails: StringChangeOptional;
    risksAndConcerns: StringChangeOptional;
}

export interface ReferralTaskPendingStatusCommandDto extends ServiceRecipientTaskBaseCommandDto {
    pendingStatus: NumberChangeOptional;
}

export interface ReferralTaskAssessmentDateCommandDto extends ServiceRecipientTaskBaseCommandDto {
    interviewer1: NumberChangeOptional;
    interviewer2: NumberChangeOptional;
    decisionDate: StringChangeOptional;
    firstOfferedInterviewDate: StringChangeOptional;
    location: StringChangeOptional;
    interviewSetupComments: StringChangeOptional;
    interviewDna: NumberChangeOptional;
    interviewDnaComments: StringChangeOptional;

}

export interface ReferralTaskFundingCommandDto extends ServiceRecipientTaskBaseCommandDto {
    fundingSource: NumberChangeOptional;
    fundingPaymentRef: StringChangeOptional;
    fundingAmount: NumberChangeOptional;
    fundingReviewDate: StringChangeOptional;
    fundingDecisionDate: StringChangeOptional;
    hoursOfSupport: NumberChangeOptional;
    fundingAccepted: BooleanChange;
}

export interface ReferralTaskReferralDetailsCommandDto extends ServiceRecipientTaskBaseCommandDto {
    receivedDateChange: StringChangeOptional;
    srcGeographicAreaChange: NumberChangeOptional;
}

export interface ReferralTaskWaitingListScoreCommandDto extends ServiceRecipientTaskBaseCommandDto {
    scoreChange: NumberChangeOptional;
}

export interface ReferralTaskDeliveredByCommandDto extends ServiceRecipientTaskBaseCommandDto {
    deliveredBy: NumberChangeOptional;
    /** local date */
    deliveredByStartDate: StringChangeOptional;
}

export interface ReferralTaskAllocateWorkerCommandDto extends ServiceRecipientTaskBaseCommandDto {
    allocatedWorkerContactId: NumberChangeOptional;
}

export interface ReferralTaskStartOnServiceCommandDto extends ReferralTaskAllocateWorkerCommandDto {
    hasStarted: BooleanChange; // retain for history
    receivingServiceDate: StringChangeOptional;
}

/** see com.ecco.webApi.taskFlow.ReferralTaskEditDataProtectionCommandViewModel */
export interface ReferralTaskEditSignedAgreementCommandDto extends ServiceRecipientTaskBaseCommandDto {
    agreementDateChange: StringChangeOptional;
    agreementStatusChange: BooleanChange;
    signatureSvgXml?: string | undefined;
    reset?: boolean | undefined;
    signedDateChange: StringChangeOptional;
}

/**
 * Command for tasks 'initial sp data' and 'exit sp data'
 * see com.ecco.webApi.taskFlow.ReferralTaskSPDataCommandViewModel
 */
export interface ReferralTaskSPDataCommandDto extends ServiceRecipientTaskBaseCommandDto {
    choicesMapChanges?: {[key: string]: NumberChangeOptional} | undefined;
    textMapChanges?: {[key: string]: StringChangeOptional} | undefined;
}

/**
 * Command for tasks 'close'
 * see com.ecco.webApi.taskFlow.ReferralTaskExitCommandViewModel
 */
export interface ReferralTaskExitCommandDto extends ServiceRecipientTaskBaseCommandDto {
    exitedUndoCloseOff?: boolean | undefined;
    exitedDateChange: StringChangeOptional;
    exitedReasonChange: NumberChangeOptional;
    exitedCommentChange: StringChangeOptional;
    reviewDateChange: StringChangeOptional; // incidents, piggy-backing referral
}

/**
 * Command for tasks 'close'
 * see com.ecco.webApi.taskFlow.ReferralTaskScheduleReviewsCommandViewModel
 */
export interface ReferralTaskScheduleReviewsCommandDto extends ServiceRecipientTaskBaseCommandDto {
    defaultDatesFromSchedule?: string | undefined;
    customDateChange?: StringChangeOptional | undefined;
}

/**
 * Base Command for tasks 'accept on service' and 'appropriate referral'
 */
export interface BaseAcceptCommandDto extends ServiceRecipientTaskBaseCommandDto {
    acceptedDate: StringChangeOptional;
    signpostedBack: BooleanChange;
    signpostedReason: NumberChangeOptional;
    signpostedAgency: NumberChangeOptional;
    signpostedComment: StringChangeOptional;
    acceptedState: StringChangeOptional;
}

export type UserAccessAuditLevel = "STATUS" | "SHORT_HISTORY" | "FULL_HISTORY"

/**
 * See UserAccessAuditCommandDto
 */
export type SourceAudit = undefined | "qr";
export interface UserAccessAuditCommandDto extends ServiceRecipientTaskBaseCommandDto {
    level: UserAccessAuditLevel;
    source: SourceAudit;
}