import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {sessionData} from "../../__tests__/testUtils";
import {BuildingEditor} from "../../buildings/BuildingForm";
import {Command} from "ecco-commands";
import {CommandFormTest, CommandFormTestOutput} from "../../cmd-queue/testUtils";
import {CommandForm} from "../../cmd-queue/CommandForm";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {Address, AddressedLocationAjaxRepository, Building, BuildingAjaxRepository, ContractAjaxRepository} from "ecco-dto";
import RateCardsView from "../../contracts/RateCardsView";

const contractRepository = getFailAllMethodsMock(ContractAjaxRepository);
contractRepository.findRateCardsForContract = (contractId: number) => Promise.resolve(rateCards);
contractRepository.findRateCardById = (rateCardId: number) => Promise.resolve(rateCards.filter(rc => rc.rateCardId == rateCardId).pop());

const overrides = {
    sessionData: sessionData,
    contractRepository: contractRepository
} as any as EccoAPI;

describe("RateCard tests", () => {

    it("building new", () => {
        // DEBUG - set this high or null to see the cmd output for longer/forever
        // currently needs to clear to show a command wasn't emitted - see "delete new one" below
        const delayClearOnFlushFinish = 2500;

        mount(
                <TestServicesContextProvider overrides={overrides}>
                    <CommandFormTest delayClearOnFinishFlush={delayClearOnFlushFinish}>
                        {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                                <>
                                    <RateCardsView contractId={1}/>
                                    <CommandFormTestOutput
                                            cmdEmitted={cmdEmitted}
                                            cmdEmittedDraft={cmdEmittedDraft}
                                    />
                                </>
                        )}
                    </CommandFormTest>
                </TestServicesContextProvider>
        );
    });
});

// charge types
// unit of measurements
const unitOfMeasurements = [
    {
        id: 1,
        name: "minute",
        unitMeasurement: "MINUTE",
        units: 1,
        description: "minute"
    },
    {
        id: 2,
        name: "hour",
        unitMeasurement: "HOUR",
        units: 1,
        description: "hour"
    },
    {
        id: 3,
        name: "day",
        unitMeasurement: "DAY",
        units: 1,
        description: "day"
    },
    {
        id: 4,
        name: "month",
        unitMeasurement: "MONTH",
        units: 1,
        description: "month"
    }
];
const rateCards = [
    {
        rateCardId: 1,
        name: "standard",
        startDateTime: "2025-01-01T00:00:00",
        endDateTime: "2026-01-01T00:00:00",
        chargeNameId: 219,
        rateCardEntries: [],
        contracts: [1],
        matchingPartsOfWeek: null,
        matchingStartTime: null,
        matchingEndTime: null
    }
];
