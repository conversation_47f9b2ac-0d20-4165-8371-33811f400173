import {
    <PERSON>ole<PERSON><PERSON>hang<PERSON>,
    CommandDto,
    NumberChangeOptional,
    ReferralDto,
    ServiceDto,
    StringChangeOptional
} from "ecco-dto";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {CommandDtoServer} from "ecco-dto/evidence-dto";
import {PrefixType} from "ecco-dto/service-recipient-dto";
import {
    BaseAcceptCommandDto,
    DaysAttendingUpdateCommandDto,
    DeleteRequestServiceRecipientCommandDto,
    DeleteServiceRecipientCommandDto,
    MoveServiceRecipientCommandDto,
    ReferralTaskAllocateServiceCommandDto,
    ReferralTaskAllocateWorkerCommandDto,
    ReferralTaskAssessmentDateCommandDto,
    ReferralTaskExitCommandDto,
    ReferralTaskDeliveredByCommandDto,
    ReferralTaskEditDestinationCommandDto,
    ReferralTaskEditDetailsCommandDto,
    ReferralTaskEditEmergencyDetailsCommandDto,
    ReferralTaskEditSignedAgreementCommandDto,
    ReferralTaskEditSourceCommandDto,
    ReferralTaskFundingCommandDto,
    ReferralTaskJoinCommandDto,
    ReferralTaskPendingStatusCommandDto,
    ReferralTaskReferralDetailsCommandDto,
    ReferralTaskScheduleReviewsCommandDto,
    ReferralTaskSPDataCommandDto,
    ReferralTaskStartOnServiceCommandDto,
    ReferralTaskWaitingListScoreCommandDto,
    ServiceRecipientAssociatedContactCommandDto
} from "ecco-dto/evidence/evidence-command-dto";
import {ServicesDto} from "ecco-dto/service-config-dto";
import {BaseUpdateCommand} from "../cmd-queue/commands";

/* This module is for commands to do with updates to the referral, but not associated with evidence screens */

export type Operation = "add" | "update" | "remove";

// see CreateReferralCommandViewModel.java
export interface CreateReferralCommandDto extends CommandDto, CommandDtoServer {
    prefix: PrefixType;
    referralViewModel: ReferralDto;
}

// see CreateServiceRecipientCommandViewModel
export abstract class CreateServiceRecipientCommand extends BaseUpdateCommand {
    constructor(protected prefix: PrefixType, uri: string) {
        super(uri);
    }
}
export class CreateReferralCommand extends CreateServiceRecipientCommand {

    private static prefix: PrefixType = "r";
    public static discriminator = "createSvcRec"; // as per CreateServiceRecipientCommand

    constructor(protected referral: ReferralDto) {
        super(CreateReferralCommand.prefix, `service-recipient/command/create/${CreateReferralCommand.prefix}/`);
    }

    public toDto(): CreateReferralCommandDto {
        return ({
                commandName: CreateReferralCommand.discriminator,
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                prefix: this.prefix,
                referralViewModel: this.referral
            });
    }
}

// used to create the above delete dtos
export class DeleteRequestServiceRecipientCommand extends BaseUpdateCommand {
    public static discriminator = "deleteReqSvcRec";

    constructor(private serviceRecipientId: number,
                private reason: string, private revoke: boolean,
                private deleteParentIfPossible: boolean) {
        super(`service-recipient/${serviceRecipientId.toString()}/delete-request/`);
    }

    public toDto(): DeleteRequestServiceRecipientCommandDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                serviceRecipientId: this.serviceRecipientId,
                revoke: this.revoke,
                reason: this.reason,
                deleteParentIfPossible: this.deleteParentIfPossible
        });
    }
}

// used to create the above delete dtos
export class DeleteServiceRecipientCommand extends BaseUpdateCommand {
    public static discriminator = "deleteSvcRec";

    constructor(private serviceRecipientId: number, private requestDeletionUuid: string,
                private reason: string, private deleteParentIfPossible: boolean, private jsonViewModel: string) {
        super(`service-recipient/${serviceRecipientId.toString()}/delete/`);
    }

    public toDto(): DeleteServiceRecipientCommandDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                serviceRecipientId: this.serviceRecipientId,
                requestDeletionUuid: this.requestDeletionUuid,
                jsonViewModel: this.jsonViewModel,
                reason: this.reason,
                deleteParentIfPossible: this.deleteParentIfPossible
        });
    }
}

// used to move a srId to another parent (eg referral to a client)
export class MoveServiceRecipientCommand extends BaseUpdateCommand {
    public static discriminator = "moveSvcRec";

    constructor(
        private serviceRecipientId: number,
        private reason: string,
        private parentId?: number,
        private deleteParentIfPossible?: boolean,
        private serviceAllocationId?: number
    ) {
        super(`service-recipient/${serviceRecipientId.toString()}/move/`);
    }

    public toDto(): MoveServiceRecipientCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            parentId: this.parentId,
            deleteParentIfPossible: this.deleteParentIfPossible,
            serviceAllocationId: this.serviceAllocationId,
            reason: this.reason
        };
    }
}

export class ServiceRecipientAssociatedContactCommand extends BaseUpdateCommand {
    private archivedChange: StringChangeOptional;
    private addedAssociatedTypeIds: StringChangeOptional;
    private removedAssociatedTypeIds: StringChangeOptional;

    private associatedServiceRecipientId: number | undefined;
    private associatedRelationship: NumberChangeOptional;

    constructor(
        private operation: "add" | "update",
        private serviceRecipientId: number,
        private contactId: number
    ) {
        super(
            `service-recipients/${serviceRecipientId.toString()}/contact/${contactId.toString()}/command/`
        );
    }

    public changeArchived(from: EccoDate, to: EccoDate) {
        this.archivedChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeAddedAssociatedTypeIds(from: string, to: string) {
        this.addedAssociatedTypeIds = this.asStringChange(from, to);
        return this;
    }

    public changeRemovedAssociatedTypeIds(from: string, to: string) {
        this.removedAssociatedTypeIds = this.asStringChange(from, to);
        return this;
    }

    public withAssociatedServiceRecipientId(associatedServiceRecipientId: number) {
        this.associatedServiceRecipientId = associatedServiceRecipientId;
    }
    public changeAssociatedRelationshipType(from: number, to: number) {
        this.associatedRelationship = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges(): boolean {
        return (
            this.archivedChange != null ||
            this.addedAssociatedTypeIds != null ||
            this.removedAssociatedTypeIds != null ||
            this.associatedRelationship != null ||
            this.operation == "add"
        );
    }

    public toDto(): ServiceRecipientAssociatedContactCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            operation: this.operation,
            contactId: this.contactId,
            addedAssociatedTypeIds: this.addedAssociatedTypeIds,
            removedAssociatedTypeIds: this.removedAssociatedTypeIds,
            archivedChange: this.archivedChange,
            associatedServiceRecipientId: this.associatedServiceRecipientId,
            associatedRelationship: this.associatedRelationship
        };
    }
}

/** Also see ServiceRecipientTaskBaseCommandDto */
export abstract class BaseServiceRecipientTaskUpdateCommand extends BaseUpdateCommand {
    public static discriminator = "taskUpdate";

    protected userComment?: string;

    protected taskInstanceId?: string;

    /**
     * Client file 'tasks' that can be performed. Actual workflow tasks are also be updated via here.
     * @param operation add/update etc
     * @param serviceRecipientId
     * @param taskName the 'tasks' task name (eg 'decideFinal') or 'taskInstance' for an actual workflow task command
     * @param taskInstanceId A task identifier for activiti/linear
     */
    constructor(
        private operation: Operation,
        protected serviceRecipientId: number,
        protected taskName: string,
        protected taskHandle?: string
    ) {
        super(`service-recipients/${serviceRecipientId.toString()}/tasks/${taskName}/`);
        if (operation != "add" && operation != "remove" && operation != "update") {
            throw new Error("Illegal param 'operation': " + operation);
        }
    }

    public withComment(comment?: string) {
        this.userComment = comment && comment.length ? comment : undefined;
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return this.userComment != null || this.operation == "add" || this.operation == "remove";
    }

    public getOperation() {
        return this.operation;
    }
}

export class DaysAttendingUpdateCommand extends BaseServiceRecipientTaskUpdateCommand {
    private daysAttendingChange: NumberChangeOptional;

    /** operation should be either "add", "remove" or "update" */
    constructor(
        operation: Operation,
        serviceRecipientId: number,
        from: number,
        to: number,
        taskHandle: string
    ) {
        super(operation, serviceRecipientId, "days attending", taskHandle);
        if (from == to) {
            throw new Error("don't create a command where from==to");
        }
        this.daysAttendingChange = {from: from, to: to};
    }

    public toDto(): DaysAttendingUpdateCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            daysAttendingChange: this.daysAttendingChange
        };
    }
}

export class ReferralTaskJoinCommand extends BaseServiceRecipientTaskUpdateCommand {
    private parentServiceRecipientId: NumberChangeOptional;

    constructor(serviceRecipientId: number, taskHandle?: string) {
        super("update", serviceRecipientId, "join", taskHandle);
    }

    changeParentServiceRecipientId(from: number, to: number) {
        this.parentServiceRecipientId = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.parentServiceRecipientId != null;
    }

    public toDto(): ReferralTaskJoinCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            parentServiceRecipientId: this.parentServiceRecipientId
        };
    }
}

export class ReferralTaskAllocateServiceCommand extends BaseServiceRecipientTaskUpdateCommand {
    private allocations: ServicesDto;
    private allocationIds: number[] = [];

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "allocate", taskHandle);
        this.allocations = {services: []};
    }

    addService(serviceId: number) {
        if (serviceId != null) {
            this.allocations.services.push({id: serviceId} as ServiceDto);
        }
        return this;
    }

    addServiceAllocationId(svcCatId: number) {
        if (svcCatId != null) {
            this.allocationIds.push(svcCatId);
        }
        return this;
    }

    public toDto(): ReferralTaskAllocateServiceCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            allocations: this.allocations,
            allocationIds: this.allocationIds
        };
    }
}

// used to create the above ReferralTaskEditDetailsCommandDto
export class EditReferralDetailsCommand extends BaseServiceRecipientTaskUpdateCommand {
    private choicesMapChanges?: {[key: string]: NumberChangeOptional};

    private daysAttendingChange: NumberChangeOptional;

    private exitedDateChange: StringChangeOptional;

    private isPrimaryChildReferralChange: BooleanChange;

    private projectChange: NumberChangeOptional;

    private receivingServiceDateChange: StringChangeOptional;

    private textMapChanges?: {[key: string]: StringChangeOptional};

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "editDetails", taskHandle);
    }

    /** Add choices map change */
    public changeChoicesMapEntry(key: string, from: number, to: number) {
        let change = this.asNumberChange(from, to);
        if (change) {
            if (!this.choicesMapChanges) {
                this.choicesMapChanges = {};
            }
            this.choicesMapChanges[key] = change;
        }
        return this;
    }

    public changeDaysAttending(from: number, to: number) {
        this.daysAttendingChange = this.asNumberChange(from, to);
        return this;
    }

    /** Add change data, but only if to != from */
    public changeExitedDate(from: EccoDate, to: EccoDate) {
        this.exitedDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeIsPrimaryChildReferral(from: boolean, to: boolean) {
        this.isPrimaryChildReferralChange = this.asBooleanChange(from, to);
    }

    public changeReceivingServiceDate(from: EccoDate, to: EccoDate) {
        this.receivingServiceDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeProject(from: number, to: number) {
        this.projectChange = this.asNumberChange(from, to);
        return this;
    }

    /** Add text map change data, but only if to != from */
    public changeTextMapEntry(key: string, from: string, to: string) {
        let change = this.asStringChange(from, to);
        if (change) {
            if (!this.textMapChanges) {
                this.textMapChanges = {};
            }
            this.textMapChanges[key] = change;
        }
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.daysAttendingChange != null ||
            this.exitedDateChange != null ||
            this.isPrimaryChildReferralChange != null ||
            this.projectChange != null ||
            this.receivingServiceDateChange != null ||
            this.choicesMapChanges != null ||
            this.textMapChanges != null
        );
    }

    public toDto(): ReferralTaskEditDetailsCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            choicesMapChanges: this.choicesMapChanges,
            daysAttendingChange: this.daysAttendingChange,
            exitedDateChange: this.exitedDateChange,
            isPrimaryChildReferralChange: this.isPrimaryChildReferralChange,
            projectChange: this.projectChange,
            receivingServiceDateChange: this.receivingServiceDateChange,
            textMapChanges: this.textMapChanges
        };
    }
}

export class EditReferralSourceCommand extends BaseServiceRecipientTaskUpdateCommand {
    private agencyChange: NumberChangeOptional;
    private referrerChange: NumberChangeOptional;
    private selfReferralChange: BooleanChange;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "editSource", taskHandle);
    }

    public changeAgency(from: number, to: number) {
        this.agencyChange = this.asNumberChange(from, to);
        return this;
    }

    public changeReferrer(from: number, to: number) {
        this.referrerChange = this.asNumberChange(from, to);
        return this;
    }

    public changeSelfReferral(from: boolean, to: boolean) {
        this.selfReferralChange = this.asBooleanChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.agencyChange != null ||
            this.referrerChange != null ||
            this.selfReferralChange != null
        );
    }

    public toDto(): ReferralTaskEditSourceCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            agencyChange: this.agencyChange,
            referrerChange: this.referrerChange,
            selfReferralChange: this.selfReferralChange
        };
    }
}

export class EditReferralDestinationCommand extends BaseServiceRecipientTaskUpdateCommand {
    private projectChange: NumberChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "editDestination", taskHandle);
    }

    public changeProject(from: number, to: number) {
        this.projectChange = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.projectChange != null;
    }

    public toDto(): ReferralTaskEditDestinationCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            projectChange: this.projectChange
        };
    }
}

export class EditEmergencyDetailsCommand extends BaseServiceRecipientTaskUpdateCommand {
    private descriptionDetailsChange: StringChangeOptional;
    private communicationNeedsChange: StringChangeOptional;
    private emergencyKeywordChange: StringChangeOptional;
    private emergencyDetailsChange: StringChangeOptional;
    private medicationDetailsChange: StringChangeOptional;
    private doctorDetailsChange: StringChangeOptional;
    private dentistDetailsChange: StringChangeOptional;
    private risksAndConcernsChange: StringChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "emergencyDetails", taskHandle);
    }

    public changeDescriptionDetails(from: string, to: string) {
        this.descriptionDetailsChange = this.asStringChange(from, to);
        return this;
    }
    public changeDoctorDetails(from: string, to: string) {
        this.doctorDetailsChange = this.asStringChange(from, to);
        return this;
    }
    public changeDentistDetails(from: string, to: string) {
        this.dentistDetailsChange = this.asStringChange(from, to);
        return this;
    }
    public changeEmergencyDetails(from: string, to: string) {
        this.emergencyDetailsChange = this.asStringChange(from, to);
        return this;
    }
    public changeEmergencyKeyword(from: string, to: string) {
        this.emergencyKeywordChange = this.asStringChange(from, to);
        return this;
    }
    public changeCommunicationNeeds(from: string, to: string) {
        this.communicationNeedsChange = this.asStringChange(from, to);
        return this;
    }
    public changeMedicationDetails(from: string, to: string) {
        this.medicationDetailsChange = this.asStringChange(from, to);
        return this;
    }
    public changeRisksAndConcerns(from: string, to: string) {
        this.risksAndConcernsChange = this.asStringChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.descriptionDetailsChange != null ||
            this.communicationNeedsChange != null ||
            this.emergencyKeywordChange != null ||
            this.emergencyDetailsChange != null ||
            this.medicationDetailsChange != null ||
            this.doctorDetailsChange != null ||
            this.risksAndConcernsChange != null ||
            this.dentistDetailsChange != null
        );
    }

    public toDto(): ReferralTaskEditEmergencyDetailsCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            doctorDetails: this.doctorDetailsChange,
            descriptionDetails: this.descriptionDetailsChange,
            dentistDetails: this.dentistDetailsChange,
            risksAndConcerns: this.risksAndConcernsChange,
            emergencyDetails: this.emergencyDetailsChange,
            emergencyKeyword: this.emergencyKeywordChange,
            medicationDetails: this.medicationDetailsChange,
            communicationNeeds: this.communicationNeedsChange
        };
    }
}

export class PendingStatusCommand extends BaseServiceRecipientTaskUpdateCommand {
    private pendingStatusIdChange: NumberChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "pendingStatus", taskHandle);
    }

    public changePendingStatusId(from: number, to: number) {
        this.pendingStatusIdChange = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.pendingStatusIdChange != null;
    }

    public toDto(): ReferralTaskPendingStatusCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskHandle: this.taskHandle,
            taskName: this.taskName,
            userComment: this.userComment,
            pendingStatus: this.pendingStatusIdChange
        };
    }
}

export class AssessmentDateCommand extends BaseServiceRecipientTaskUpdateCommand {
    private interviewer1Change: NumberChangeOptional;
    private interviewer2Change: NumberChangeOptional;
    private decisionDateChange: StringChangeOptional;
    private firstOfferedInterviewDateChange: StringChangeOptional;
    private locationChange: StringChangeOptional;
    private interviewSetupCommentsChange: StringChangeOptional;
    private interviewDnaChange: NumberChangeOptional;
    private interviewDnaCommentsChange: StringChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "assessmentDate", taskHandle);
    }

    public changeInterviewer1ContactId(from: number, to: number) {
        this.interviewer1Change = this.asNumberChange(from, to);
        return this;
    }

    public changeInterviewer2ContactId(from: number, to: number) {
        this.interviewer2Change = this.asNumberChange(from, to);
        return this;
    }

    public changeDecisionDate(from: EccoDateTime, to: EccoDateTime) {
        this.decisionDateChange = this.asLocalDateTimeChange(from, to);
        return this;
    }

    public changeFirstOfferedInterviewDate(from: EccoDate, to: EccoDate) {
        this.firstOfferedInterviewDateChange = this.asDateChange(from, to);
        return this;
    }

    public changeLocation(from: string, to: string) {
        this.locationChange = this.asStringChange(from, to);
        return this;
    }

    public changeInterviewSetupComments(from: string, to: string) {
        this.interviewSetupCommentsChange = this.asStringChange(from, to);
        return this;
    }

    public changeInterviewDna(from: number, to: number) {
        this.interviewDnaChange = this.asNumberChange(from, to);
        return this;
    }

    public changeInterviewDnaComments(from: string, to: string) {
        this.interviewDnaCommentsChange = this.asStringChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.interviewer1Change != null ||
            this.interviewer2Change != null ||
            this.decisionDateChange != null ||
            this.firstOfferedInterviewDateChange != null ||
            this.locationChange != null ||
            this.interviewSetupCommentsChange != null ||
            this.interviewDnaChange != null ||
            this.interviewDnaCommentsChange != null
        );
    }

    public toDto(): ReferralTaskAssessmentDateCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            interviewer1: this.interviewer1Change,
            interviewer2: this.interviewer2Change,
            decisionDate: this.decisionDateChange,
            firstOfferedInterviewDate: this.firstOfferedInterviewDateChange,
            location: this.locationChange,
            interviewSetupComments: this.interviewSetupCommentsChange,
            interviewDna: this.interviewDnaChange,
            interviewDnaComments: this.interviewDnaCommentsChange
        };
    }
}

export class FundingCommand extends BaseServiceRecipientTaskUpdateCommand {
    private fundingSourceChange: NumberChangeOptional;
    private fundingPaymentRefChange: StringChangeOptional;
    private fundingAmountChange: NumberChangeOptional;
    private fundingDecisionDateChange: StringChangeOptional;
    private fundingReviewDateChange: StringChangeOptional;
    private hoursOfSupportChange: NumberChangeOptional;
    private fundingAcceptedChange: BooleanChange;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "funding", taskHandle);
    }

    public changeFundingSource(from: number, to: number) {
        this.fundingSourceChange = this.asNumberChange(from, to);
        return this;
    }

    public changeFundingPaymentRef(from: string, to: string) {
        this.fundingPaymentRefChange = this.asStringChange(from, to);
        return this;
    }

    public changeFundingAmount(from: number, to: number) {
        this.fundingAmountChange = this.asNumberChange(from, to);
        return this;
    }

    public changeFundingDecisionDate(from: EccoDate, to: EccoDate) {
        this.fundingDecisionDateChange = this.asDateChange(from, to);
        return this;
    }

    public changeFundingReviewDate(from: EccoDate, to: EccoDate) {
        this.fundingReviewDateChange = this.asDateChange(from, to);
        return this;
    }

    public changeHoursOfSupport(from: number, to: number) {
        this.hoursOfSupportChange = this.asNumberChange(from, to);
        return this;
    }

    public changeFundingAccepted(from: boolean, to: boolean) {
        this.fundingAcceptedChange = this.asBooleanChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.fundingSourceChange != null ||
            this.fundingPaymentRefChange != null ||
            this.fundingAmountChange != null ||
            this.fundingReviewDateChange != null ||
            this.fundingDecisionDateChange != null ||
            this.hoursOfSupportChange != null ||
            this.fundingAcceptedChange != null
        );
    }

    public toDto(): ReferralTaskFundingCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            fundingSource: this.fundingSourceChange,
            fundingPaymentRef: this.fundingPaymentRefChange,
            fundingAmount: this.fundingAmountChange,
            fundingReviewDate: this.fundingReviewDateChange,
            fundingDecisionDate: this.fundingDecisionDateChange,
            hoursOfSupport: this.hoursOfSupportChange,
            fundingAccepted: this.fundingAcceptedChange
        };
    }
}

export class ReferralDetailsCommand extends BaseServiceRecipientTaskUpdateCommand {
    private receivedDateChange: StringChangeOptional;
    private srcGeographicAreaChange: NumberChangeOptional;

    constructor(serviceRecipientId: number, taskHandle?: string) {
        super("update", serviceRecipientId, "referralDetails", taskHandle);
    }

    public changeRecievedDate(from: EccoDate, to: EccoDate) {
        this.receivedDateChange = this.asDateChange(from, to);
        return this;
    }
    public changeSrcGeographicArea(from: number, to: number) {
        this.srcGeographicAreaChange = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.receivedDateChange != null ||
            this.srcGeographicAreaChange != null
        );
    }

    public toDto(): ReferralTaskReferralDetailsCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            receivedDateChange: this.receivedDateChange,
            srcGeographicAreaChange: this.srcGeographicAreaChange
        };
    }
}

export class WaitingListScoreCommand extends BaseServiceRecipientTaskUpdateCommand {
    private scoreChange: NumberChangeOptional;

    constructor(serviceRecipientId: number, taskHandle?: string) {
        super("update", serviceRecipientId, "waitingListCriteria", taskHandle);
    }

    public changeScore(from: number, to: number) {
        this.scoreChange = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.scoreChange != null;
    }

    public toDto(): ReferralTaskWaitingListScoreCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            scoreChange: this.scoreChange
        };
    }
}

export class ReferralTaskDeliveredByCommand extends BaseServiceRecipientTaskUpdateCommand {
    private deliveredByChange: NumberChangeOptional;
    private deliveredByStartDateChange: StringChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "deliveredBy", taskHandle);
    }

    public changeDeliveredByContactId(from: number, to: number) {
        this.deliveredByChange = this.asNumberChange(from, to);
        return this;
    }

    public changeDeliveredByDate(from: EccoDate, to: EccoDate) {
        this.deliveredByStartDateChange = this.asDateChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.deliveredByChange != null ||
            this.deliveredByStartDateChange != null
        );
    }

    public toDto(): ReferralTaskDeliveredByCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            deliveredBy: this.deliveredByChange,
            deliveredByStartDate: this.deliveredByStartDateChange
        };
    }
}

export class AllocateWorkerCommand extends BaseServiceRecipientTaskUpdateCommand {
    protected allocatedWorkerContactIdChange: NumberChangeOptional;
    public static taskName = "allocateWorker";

    constructor(
        operation: Operation,
        serviceRecipientId: number,
        taskName: string,
        taskHandle: string
    ) {
        super(operation, serviceRecipientId, taskName, taskHandle);
    }

    public changeAllocatedWorkerContactId(from: number, to: number) {
        this.allocatedWorkerContactIdChange = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.allocatedWorkerContactIdChange != null;
    }

    public toDto(): ReferralTaskAllocateWorkerCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            allocatedWorkerContactId: this.allocatedWorkerContactIdChange
        };
    }
}

export class StartOnServiceCommand extends AllocateWorkerCommand {
    private receivingServiceDateChange: StringChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "startOnService", taskHandle);
    }

    public changeReceivingServiceDate(from: EccoDate, to: EccoDate) {
        this.receivingServiceDateChange = this.asDateChange(from, to);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.receivingServiceDateChange != null;
    }

    public override toDto(): ReferralTaskStartOnServiceCommandDto {
        const allocateWorkerDto = super.toDto();
        let startOnServiceDto = allocateWorkerDto as ReferralTaskStartOnServiceCommandDto;
        return {
            ...startOnServiceDto,
            receivingServiceDate: this.receivingServiceDateChange
        };
    }
}

export abstract class EditSignedAgreementCommand extends BaseServiceRecipientTaskUpdateCommand {
    private signatureSvgXml?: string;
    private reset?: boolean;
    private signedDateChange: StringChangeOptional;
    private agreementDateChange: StringChangeOptional;
    private agreementStatusChange: BooleanChange;

    constructor(serviceRecipientId: number, taskName: string, taskHandle: string) {
        super("update", serviceRecipientId, taskName, taskHandle);
    }

    public changeAgreementDate(from: EccoDateTime, to: EccoDateTime) {
        this.agreementDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeAgreementStatus(from: boolean, to: boolean) {
        this.agreementStatusChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeSignedDate(from: EccoDateTime, to: EccoDateTime) {
        this.signedDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public withSignature(svgXml: string) {
        this.signatureSvgXml = svgXml;
        return this;
    }

    public withReset() {
        this.reset = true;
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.reset != null ||
            this.agreementDateChange != null ||
            this.agreementStatusChange != null ||
            this.signatureSvgXml != null ||
            this.signedDateChange != null
        );
    }

    public toDto(): ReferralTaskEditSignedAgreementCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            agreementDateChange: this.agreementDateChange,
            agreementStatusChange: this.agreementStatusChange,
            signedDateChange: this.signedDateChange,
            signatureSvgXml: this.signatureSvgXml,
            reset: this.reset
        };
    }
}

export class EditDataProtectionCommand extends EditSignedAgreementCommand {
    public static override discriminator = "dataProtection"; // as per referralaspects and ReferralTaskEditDataProtectionCommandViewModel.TASK_NAME
    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "dataProtection", taskHandle);
    }
}

export class EditConsentCommand extends EditSignedAgreementCommand {
    public static override discriminator = "consent"; // as per referralaspects and ReferralTaskEditConsentCommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "consent", taskHandle);
    }
}

export class EditAgreement1Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement"; // as per referralaspects and ReferralTaskEditAgreement1CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement", taskHandle);
    }
}
export class EditAgreement2Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement2"; // as per referralaspects and ReferralTaskEditAgreement2CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement2", taskHandle);
    }
}
export class EditAgreement3Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement3"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement3", taskHandle);
    }
}
export class EditAgreement4Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement4"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement4", taskHandle);
    }
}
export class EditAgreement5Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement5"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement5", taskHandle);
    }
}
export class EditAgreement6Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement6"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement6", taskHandle);
    }
}
export class EditAgreement7Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement7"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement7", taskHandle);
    }
}
export class EditAgreement8Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement8"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement8", taskHandle);
    }
}
export class EditAgreement9Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement9"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement9", taskHandle);
    }
}
export class EditAgreement10Command extends EditSignedAgreementCommand {
    public static override discriminator = "agreement10"; // as per referralaspects and ReferralTaskEditAgreement3CommandViewModel.TASK_NAME

    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, "agreement10", taskHandle);
    }
}

// used to create the above ReferralTaskSPDetailsCommandDto
export class ReferralTaskSPDataCommand extends BaseServiceRecipientTaskUpdateCommand {
    private choicesMapChanges?: {[key: string]: NumberChangeOptional};
    private textMapChanges?: {[key: string]: StringChangeOptional};

    /** Add choices map change */
    public changeChoicesMapEntry(key: string, from: number, to: number) {
        let change = this.asNumberChange(from, to);
        if (change) {
            if (!this.choicesMapChanges) {
                this.choicesMapChanges = {};
            }
            this.choicesMapChanges[key] = change;
        }
        return this;
    }

    /** Add choices map change */
    public changeTextMapEntry(key: string, from: string, to: string) {
        let change = this.asStringChange(from, to);
        if (change) {
            if (!this.textMapChanges) {
                this.textMapChanges = {};
            }
            this.textMapChanges[key] = change;
        }
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.choicesMapChanges != null || this.textMapChanges != null;
    }

    public toDto(): ReferralTaskSPDataCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            choicesMapChanges: this.choicesMapChanges,
            textMapChanges: this.textMapChanges
        };
    }
}

// used to create the above ReferralTaskExitCommandDto
export class ReferralTaskExitCommand extends BaseServiceRecipientTaskUpdateCommand {
    exitedUndoCloseOff?: boolean;
    exitedDateChange: StringChangeOptional;
    exitedReasonChange: NumberChangeOptional;
    exitedCommentChange: StringChangeOptional;
    reviewDateChange: StringChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "close", taskHandle);
    }

    /** Add change data, but only if to != from */
    public changeExitedUndoCloseOff() {
        this.exitedUndoCloseOff = true;
        return this;
    }

    /** Add change data, but only if to != from */
    public changeExitedDate(from: EccoDate, to: EccoDate) {
        this.exitedDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeReviewDate(from: string, to: string) {
        this.reviewDateChange = this.asStringChange(from, to);
        return this;
    }

    public changeExitedReason(from: number, to: number) {
        this.exitedReasonChange = this.asNumberChange(from, to);
        return this;
    }

    public changeExitedComment(from: string, to: string) {
        this.exitedCommentChange = this.asStringChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.exitedUndoCloseOff == true ||
            this.exitedDateChange != null ||
            this.exitedReasonChange != null ||
            this.exitedCommentChange != null ||
            this.reviewDateChange != null
        );
    }

    public toDto(): ReferralTaskExitCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            exitedUndoCloseOff: this.exitedUndoCloseOff,
            exitedDateChange: this.exitedDateChange,
            exitedReasonChange: this.exitedReasonChange,
            exitedCommentChange: this.exitedCommentChange,
            reviewDateChange: this.reviewDateChange
        };
    }
}

// used to create the above ReferralTaskExitCommandDto
export class ReferralTaskScheduleReviewsCommand extends BaseServiceRecipientTaskUpdateCommand {
    scheduleDates?: string;
    customDateChange?: StringChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "scheduleReviews", taskHandle);
    }

    /** Add change data, but only if to != from */
    public changeScheduleDates(schedule: string) {
        this.scheduleDates = schedule;
        return this;
    }

    public changeCustomDate(from: EccoDate, to: EccoDate) {
        this.customDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.scheduleDates != null || this.customDateChange != null;
    }

    public toDto(): ReferralTaskScheduleReviewsCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            defaultDatesFromSchedule: this.scheduleDates,
            customDateChange: this.customDateChange
        };
    }
}


export class BaseAcceptCommand extends BaseServiceRecipientTaskUpdateCommand {
    acceptedDate: StringChangeOptional;
    signpostedBack: BooleanChange;
    signpostedReason: NumberChangeOptional;
    signpostedAgency: NumberChangeOptional;
    signpostedComment: StringChangeOptional;
    acceptedState: StringChangeOptional;

    constructor(serviceRecipientId: number, taskName: string, taskHandle: string) {
        super("update", serviceRecipientId, taskName, taskHandle);
    }

    /** Add change data, but only if to != from */
    public changeAcceptedDate(from: EccoDate | null | undefined, to: EccoDate | null | undefined) {
        this.acceptedDate = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeSignpostedBack(from: boolean, to: boolean) {
        this.signpostedBack = this.asBooleanChange(from, to);
        return this;
    }

    public changeSignpostedReason(from: number | null | undefined, to: number | null | undefined) {
        this.signpostedReason = this.asNumberChange(from, to);
        return this;
    }

    public changeSignpostedAgency(from: number | null | undefined, to: number | null | undefined) {
        this.signpostedAgency = this.asNumberChange(from, to);
        return this;
    }

    public changeSignpostedComment(from: string | null | undefined, to: string | null | undefined) {
        this.signpostedComment = this.asStringChange(from, to);
        return this;
    }

    public changeAcceptedState(from: string, to: string) {
        this.acceptedState = this.asStringChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.acceptedDate != null ||
            this.signpostedBack != null ||
            this.signpostedReason != null ||
            this.signpostedAgency != null ||
            this.signpostedComment != null ||
            this.acceptedState != null
        );
    }

    public toDto(): BaseAcceptCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            acceptedDate: this.acceptedDate,
            signpostedBack: this.signpostedBack,
            signpostedReason: this.signpostedReason,
            signpostedAgency: this.signpostedAgency,
            signpostedComment: this.signpostedComment,
            acceptedState: this.acceptedState
        };
    }
}

/**
 * Matches com.ecco.webApi.taskFlow.ReferralTaskAppropriateReferralCommandViewModel
 */
export class ReferralTaskAppropriateReferralCommand extends BaseAcceptCommand {
}

/**
 * Matches com.ecco.webApi.taskFlow.ReferralTaskAcceptOnServiceCommandViewModel
 */
export class ReferralTaskAcceptOrSignpostCommand extends BaseAcceptCommand {
}
